import os
import re
import json
import pandas as pd
import google.generativeai as genai

# ────────────────────────────────
# CONFIGURATION
# ────────────────────────────────
GEMINI_API_KEY   = "AIzaSyA_GqcIIi-9p4h8YrprkBcGt5e1ElKk7eg"
INPUT_FILE       = "preprocessed_tickets_modified.xlsx"
OUTPUT_TEXTFILE  = "cxo_analysis_new.txt"
DEBUG_LOGFILE    = "cxo_raw_response_new.log"

# ────────────────────────────────
# SETUP
# ────────────────────────────────
os.environ["GRPC_VERBOSITY"]     = "ERROR"
os.environ["GRPC_CPP_LOG_LEVEL"] = "ERROR"
genai.configure(api_key=GEMINI_API_KEY)
model = genai.GenerativeModel("gemini-1.5-pro")

# ────────────────────────────────
# LOAD PREPROCESSED TICKETS
# ────────────────────────────────
df      = pd.read_excel(INPUT_FILE)
tickets = df.to_dict(orient="records")

# ────────────────────────────────
# BUILD ANALYSIS PROMPT
# ────────────────────────────────
prompt = f"""
Now that we have preprocessed support tickets, perform a detailed analysis and return strictly valid JSON with these sections:

1. observations: array of key insights about ticket volumes, patterns, trends,applications involved,recurrent issues,
2. root_causes: array of objects {{ "cause": string, "ticket_count": number }} sorted by ticket_count descending.Have the ticket number in the root cause
3. inferences: array of strategic conclusions (3–5 bullets).Make sure you are inferencing all major features in this
4. recommendations: object where each key is a root cause and its value is another object with:
   - proactive: array of suggestions to prevent the issue.Be specific in the suggestion.Include market leading tools  to implement the recommendations.
   - reactive: array of automation/self-heal/shift-left fixes when it occurs.Be specific in the suggestion.Include market leading tools  to implement the recommendations.
5. automation_opportunities: array of high-impact opportunities for further automation or shift-left.Be specific in the suggestion.Have the ticket number and explain the automation opportunities.

Tickets:
{json.dumps(tickets, indent=2, default=str)}
""".strip()

# ────────────────────────────────
# CALL GEMINI
# ────────────────────────────────
response = model.generate_content(prompt)
raw      = response.text.strip()

# ────────────────────────────────
# DEBUG: save raw response
# ────────────────────────────────
with open(DEBUG_LOGFILE, "w") as dbg:
    dbg.write(raw)

# ────────────────────────────────
# SAFE JSON PARSE
# ────────────────────────────────
def safe_parse(s: str):
    try:
        return json.loads(s)
    except json.JSONDecodeError:
        m = re.search(r'(\{[\s\S]*\})', s)
        if m:
            try:
                return json.loads(m.group(1))
            except json.JSONDecodeError:
                pass
    return None

report = safe_parse(raw)
if report is None:
    with open(OUTPUT_TEXTFILE, "w") as f:
        f.write("=== FAILED TO PARSE JSON ===\n\n")
        f.write(raw)
    print(f"⚠️ Parsing failed; see {DEBUG_LOGFILE} and {OUTPUT_TEXTFILE}")
    exit(1)

# ────────────────────────────────
# EXTRACT SECTIONS
# ────────────────────────────────
obs         = report.get("observations", [])
root_causes = report.get("root_causes", [])
inferences  = report.get("inferences", [])
recs        = report.get("recommendations", {})
autos       = report.get("automation_opportunities", [])

# ────────────────────────────────
# WRITE OUT TEXT REPORT
# ────────────────────────────────
with open(OUTPUT_TEXTFILE, "w") as f:
    f.write("=== Observations ===\n")
    for o in obs:
        f.write(f"- {o}\n")
    f.write("\n")

    f.write("=== Root Causes ===\n")
    for rc in root_causes:
        f.write(f"- {rc.get('cause')}: {rc.get('ticket_count')} tickets\n")
    f.write("\n")

    f.write("=== Inferences ===\n")
    for inf in inferences:
        f.write(f"- {inf}\n")
    f.write("\n")

    f.write("=== Recommendations ===\n")
    for cause, detail in recs.items():
        f.write(f"-- {cause} --\n")
        f.write("  Proactive:\n")
        for p in detail.get("proactive", []):
            f.write(f"    • {p}\n")
        f.write("  Reactive / Self-Heal:\n")
        for r in detail.get("reactive", []):
            f.write(f"    • {r}\n")
        f.write("\n")

        # Automation Opportunities
    f.write("=== Automation Opportunities ===\n")
    for item in autos:
        # 1. Get the ticket number from either key
        ticket = item.get("ticket_id") or item.get("ticket") or "Unknown"

        # 2. Get the raw opportunity text
        text = item.get("opportunity", "").strip()

        # 3. Split into title + explanation
        parts = text.split(".", 1)
        title = parts[0].strip()
        explanation = parts[1].strip() if len(parts) > 1 else ""

        # 4. Write out in the desired format
        f.write(f"Ticket Number: {ticket}\n")
        f.write(f"  Opportunity: {title}.\n")
        if explanation:
            f.write(f"  Explanation: {explanation}.\n")
        f.write("\n")


print(f"✅ Analysis written to: {OUTPUT_TEXTFILE}")
print(f"🔍 Raw JSON logged to: {DEBUG_LOGFILE}")