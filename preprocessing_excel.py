import os
import re
import logging
import pandas as pd
import google.generativeai as genai
import spacy
from nltk.stem import PorterStemmer
import nltk
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from pathlib import Path
import json

# Download required NLTK data
try:
    nltk.download('punkt_tab', quiet=True)
    nltk.download("punkt", quiet=True)
except Exception as e:
    print(f"Warning: Could not download NLTK data: {e}")

@dataclass
class Config:
    """Configuration class for preprocessing parameters"""
    excel_file: str = "Masterbrands.xlsx"
    output_file: str = "preprocessed_tickets_all.xlsx"
    master_brand: str = "masterbrand"
    gemini_model: str = "gemini-1.5-pro"
    batch_size: int = 10  # Process in batches for Gemini validation
    enable_gemini_validation: bool = False  # Disable by default for efficiency
    log_level: str = "INFO"

class PreprocessingError(Exception):
    """Custom exception for preprocessing errors"""
    pass

class TicketPreprocessor:
    """Main class for ticket preprocessing operations"""

    def __init__(self, config: Config):
        self.config = config
        self.setup_logging()
        self.setup_environment()
        self.setup_nlp()
        self.setup_gemini()

    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=getattr(logging, self.config.log_level),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('preprocessing.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def setup_environment(self):
        """Setup environment variables"""
        os.environ["GRPC_VERBOSITY"] = "ERROR"
        os.environ["GRPC_CPP_LOG_LEVEL"] = "ERROR"

    def setup_nlp(self):
        """Initialize NLP components"""
        try:
            self.nlp = spacy.load("en_core_web_sm")
            self.stemmer = PorterStemmer()
            self.logger.info("NLP components initialized successfully")
        except Exception as e:
            raise PreprocessingError(f"Failed to initialize NLP components: {e}")

    def setup_gemini(self):
        """Initialize Gemini AI model"""
        try:
            api_key = os.getenv('GEMINI_API_KEY')
            if not api_key:
                self.logger.warning("GEMINI_API_KEY not found in environment variables")
                self.model = None
                return

            genai.configure(api_key=api_key)
            self.model = genai.GenerativeModel(self.config.gemini_model)
            self.logger.info("Gemini AI model initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize Gemini: {e}")
            self.model = None

    def validate_input_file(self) -> bool:
        """Validate that input file exists and is readable"""
        if not Path(self.config.excel_file).exists():
            raise PreprocessingError(f"Input file not found: {self.config.excel_file}")

        try:
            # Try to read the file to ensure it's valid
            pd.read_excel(self.config.excel_file, nrows=1)
            return True
        except Exception as e:
            raise PreprocessingError(f"Cannot read input file: {e}")

    def mask_email(self, text: str) -> str:
        """Mask email addresses in text"""
        if not isinstance(text, str):
            return text
        return re.sub(r'[\w\.-]+@[\w\.-]+\.\w+', '[EMAIL]', text)

    def mask_brand(self, text: str, brand: str = None) -> str:
        """Mask brand names in text"""
        if not isinstance(text, str):
            return text
        brand = brand or self.config.master_brand
        return re.sub(fr'\b{re.escape(brand)}\b', '[BRAND]', text, flags=re.IGNORECASE)

    def replace_inc(self, ticket_id: str) -> str:
        """Replace INC format with T format in ticket IDs"""
        if not isinstance(ticket_id, str):
            return ticket_id
        return re.sub(r'\bINC(\d+)', r'T\1', ticket_id, flags=re.IGNORECASE)

    def lemmatize_text(self, text: str) -> str:
        """Lemmatize text using spaCy"""
        if not isinstance(text, str) or not text.strip():
            return text
        try:
            doc = self.nlp(text)
            return " ".join([token.lemma_ for token in doc if not token.is_space])
        except Exception as e:
            self.logger.warning(f"Lemmatization failed for text: {e}")
            return text

    def stem_text(self, text: str) -> str:
        """Stem text using NLTK Porter Stemmer"""
        if not isinstance(text, str) or not text.strip():
            return text
        try:
            words = nltk.word_tokenize(text)
            return " ".join([self.stemmer.stem(word) for word in words])
        except Exception as e:
            self.logger.warning(f"Stemming failed for text: {e}")
            return text

# ========== MAIN PROCESSING ==========

df = pd.read_excel(EXCEL_FILE)
processed_rows = []

for idx, row in df.iterrows():
    row_data = row.to_dict()
    modified = {}

    for key, value in row_data.items():
        if isinstance(value, str):
            cleaned = value
            cleaned = mask_email(cleaned)
            cleaned = mask_brand(cleaned)
            cleaned = lemmatize_text(cleaned)
            cleaned = stem_text(cleaned)
            modified[key] = cleaned
        else:
            modified[key] = value

    # Replace INC12345 → T12345 in Ticket ID field
    ticket_id_field = next((key for key in row_data if 'ticket' in key.lower() and 'id' in key.lower()), None)
    if ticket_id_field and isinstance(row_data[ticket_id_field], str):
        modified[ticket_id_field] = replace_inc(row_data[ticket_id_field])

    # Optional Gemini QA
    try:
        prompt = f"""
        Original ticket data:
        {row_data}

        Cleaned ticket data:
        {modified}

        Does the cleaned version mask all brands, emails, and correctly format ticket IDs?
        """
        response = model.generate_content(prompt)
        modified["gemini_feedback"] = response.text.strip()
    except Exception as e:
        modified["gemini_feedback"] = f"Error: {str(e)}"

    processed_rows.append(modified)

# ========== EXPORT ==========
clean_df = pd.DataFrame(processed_rows)
clean_df.to_excel(OUTPUT_FILE, index=False)
print(f"✅ Preprocessing complete. Output saved to '{OUTPUT_FILE}'")