import os
import re
import pandas as pd
import google.generativeai as genai
import spacy
from nltk.stem import PorterStemmer
import nltk
nltk.download('punkt_tab')

# ========== SETUP ==========
GEMINI_API_KEY = "AIzaSyA_GqcIIi-9p4h8YrprkBcGt5e1ElKk7eg"  # Replace with your Gemini API key
EXCEL_FILE = "Masterbrands.xlsx"
OUTPUT_FILE = "preprocessed_tickets_all.xlsx"
MASTER_BRAND = "masterbrand"

# ====== ENVIRONMENT SETUP ======
os.environ["GRPC_VERBOSITY"] = "ERROR"
os.environ["GRPC_CPP_LOG_LEVEL"] = "ERROR"

genai.configure(api_key=GEMINI_API_KEY)
model = genai.GenerativeModel("gemini-1.5-pro")

# ====== NLP SETUP ======
nlp = spacy.load("en_core_web_sm")
stemmer = PorterStemmer()
nltk.download("punkt")

# ========== TEXT PROCESSING FUNCTIONS ==========

def mask_email(text):
    return re.sub(r'[\w\.-]+@[\w\.-]+\.\w+', '[EMAIL]', text)

def mask_brand(text, brand=MASTER_BRAND):
    return re.sub(fr'\b{re.escape(brand)}\b', '[BRAND]', text, flags=re.IGNORECASE)

def replace_inc(ticket_id):
    return re.sub(r'\bINC(\d+)', r'T\1', ticket_id, flags=re.IGNORECASE)

def lemmatize_text(text):
    doc = nlp(text)
    return " ".join([token.lemma_ for token in doc])

def stem_text(text):
    words = nltk.word_tokenize(text)
    return " ".join([stemmer.stem(word) for word in words])

# ========== MAIN PROCESSING ==========

df = pd.read_excel(EXCEL_FILE)
processed_rows = []

for idx, row in df.iterrows():
    row_data = row.to_dict()
    modified = {}

    for key, value in row_data.items():
        if isinstance(value, str):
            cleaned = value
            cleaned = mask_email(cleaned)
            cleaned = mask_brand(cleaned)
            cleaned = lemmatize_text(cleaned)
            cleaned = stem_text(cleaned)
            modified[key] = cleaned
        else:
            modified[key] = value

    # Replace INC12345 → T12345 in Ticket ID field
    ticket_id_field = next((key for key in row_data if 'ticket' in key.lower() and 'id' in key.lower()), None)
    if ticket_id_field and isinstance(row_data[ticket_id_field], str):
        modified[ticket_id_field] = replace_inc(row_data[ticket_id_field])

    # Optional Gemini QA
    try:
        prompt = f"""
        Original ticket data:
        {row_data}

        Cleaned ticket data:
        {modified}

        Does the cleaned version mask all brands, emails, and correctly format ticket IDs?
        """
        response = model.generate_content(prompt)
        modified["gemini_feedback"] = response.text.strip()
    except Exception as e:
        modified["gemini_feedback"] = f"Error: {str(e)}"

    processed_rows.append(modified)

# ========== EXPORT ==========
clean_df = pd.DataFrame(processed_rows)
clean_df.to_excel(OUTPUT_FILE, index=False)
print(f"✅ Preprocessing complete. Output saved to '{OUTPUT_FILE}'")